<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-updates/android/src/main/assets"/><source path="/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-updates/android/src/main/certificates"><file name="expo-root.pem" path="/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-updates/android/src/main/certificates/expo-root.pem"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-updates/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-updates/android/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>