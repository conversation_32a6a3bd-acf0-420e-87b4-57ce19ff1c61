{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": "."}, {"build": "src/main/cpp/worklets", "jsonFile": "directory-src.main.cpp.worklets-Debug-ece67eb1fd05b0a42e53.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 0, "source": "src/main/cpp/worklets", "targetIndexes": [1]}, {"build": "src/main/cpp/reanimated", "jsonFile": "directory-src.main.cpp.reanimated-Debug-02a55322cec7fc692a79.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 0, "source": "src/main/cpp/reanimated", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "Reanimated", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 2, "id": "reanimated::@89a6a9b85fb42923616c", "jsonFile": "target-reanimated-Debug-bbdce12c8b8ae6829ae8.json", "name": "reanimated", "projectIndex": 0}, {"directoryIndex": 1, "id": "worklets::@a0394df2d94e5212d8bd", "jsonFile": "target-worklets-Debug-aafe9b26a702c1c27f60.json", "name": "worklets", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "source": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android"}, "version": {"major": 2, "minor": 3}}