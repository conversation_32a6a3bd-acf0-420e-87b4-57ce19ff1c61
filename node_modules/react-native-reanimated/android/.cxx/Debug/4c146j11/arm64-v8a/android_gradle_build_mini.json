{"buildFiles": ["/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/hermes-engine/hermes-engineConfig.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/hermes-engine/hermes-engineConfigVersion.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/CMakeLists.txt", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/CMakeLists.txt", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"worklets::@a0394df2d94e5212d8bd": {"artifactName": "worklets", "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.arm64-v8a/libhermestooling.so"]}, "reanimated::@89a6a9b85fb42923616c": {"artifactName": "reanimated", "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.so", "runtimeFiles": ["/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.arm64-v8a/libhermestooling.so"]}}}