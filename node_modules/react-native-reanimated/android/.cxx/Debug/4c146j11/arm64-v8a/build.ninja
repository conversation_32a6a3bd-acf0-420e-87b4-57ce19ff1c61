# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Reanimated
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target worklets


#############################################
# Order-only phony target for worklets

build cmake_object_order_depends_target_worklets: phony || src/main/cpp/worklets/CMakeFiles/worklets.dir

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o: CXX_COMPILER__worklets_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  OBJECT_FILE_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target worklets


#############################################
# Link the shared library ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so

build ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so: CXX_SHARED_LIBRARY_LINKER__worklets_Debug src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.arm64-v8a/libhermestooling.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.arm64-v8a/libhermestooling.so  -latomic -lm
  OBJECT_DIR = src/main/cpp/worklets/CMakeFiles/worklets.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libworklets.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = src/main/cpp/worklets/CMakeFiles/worklets.dir/
  TARGET_FILE = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.pdb


#############################################
# Utility command for edit_cache

build src/main/cpp/worklets/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/src/main/cpp/worklets && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/main/cpp/worklets/edit_cache: phony src/main/cpp/worklets/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/main/cpp/worklets/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/src/main/cpp/worklets && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/main/cpp/worklets/rebuild_cache: phony src/main/cpp/worklets/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target reanimated


#############################################
# Order-only phony target for reanimated

build cmake_object_order_depends_target_reanimated: phony || cmake_object_order_depends_target_worklets

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o: CXX_COMPILER__reanimated_Debug /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  OBJECT_FILE_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target reanimated


#############################################
# Link the shared library ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.so

build ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.so: CXX_SHARED_LIBRARY_LINKER__reanimated_Debug src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o | ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.arm64-v8a/libhermestooling.so || ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so  -landroid  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -llog  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/libs/android.arm64-v8a/libhermestooling.so  -latomic -lm
  OBJECT_DIR = src/main/cpp/reanimated/CMakeFiles/reanimated.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreanimated.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = src/main/cpp/reanimated/CMakeFiles/reanimated.dir/
  TARGET_FILE = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.so
  TARGET_PDB = ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.pdb


#############################################
# Utility command for edit_cache

build src/main/cpp/reanimated/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/src/main/cpp/reanimated && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/main/cpp/reanimated/edit_cache: phony src/main/cpp/reanimated/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/main/cpp/reanimated/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/src/main/cpp/reanimated && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/main/cpp/reanimated/rebuild_cache: phony src/main/cpp/reanimated/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libreanimated.so: phony ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.so

build libworklets.so: phony ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so

build reanimated: phony ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.so

build worklets: phony ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a

build all: phony src/main/cpp/worklets/all src/main/cpp/reanimated/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/src/main/cpp/reanimated

build src/main/cpp/reanimated/all: phony ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libreanimated.so

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/src/main/cpp/worklets

build src/main/cpp/worklets/all: phony ../../../../build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a/libworklets.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/cmake.verify_globs | ../../../../CMakeLists.txt ../../../../src/main/cpp/reanimated/CMakeLists.txt ../../../../src/main/cpp/worklets/CMakeLists.txt ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/hermes-engine/hermes-engineConfigVersion.cmake /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../../../../src/main/cpp/reanimated/CMakeLists.txt ../../../../src/main/cpp/worklets/CMakeLists.txt ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/hermes-engine/hermes-engineConfigVersion.cmake /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
