# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# REANIMATED_COMMON_CPP_SOURCES at src/main/cpp/reanimated/CMakeLists.txt:3 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/PropsRegistry.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/reanimated/Tools/FeaturesConfig.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# WORKLETS_COMMON_CPP_SOURCES at src/main/cpp/worklets/CMakeLists.txt:3 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Registries/EventHandlerRegistry.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/SharedItems/Shareables.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/AsyncQueue.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSISerializer.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSLogger.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/JSScheduler.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/ReanimatedVersion.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/UIScheduler.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/Tools/WorkletEventHandler.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# REANIMATED_ANDROID_CPP_SOURCES at src/main/cpp/reanimated/CMakeLists.txt:5 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# WORKLETS_ANDROID_CPP_SOURCES at src/main/cpp/worklets/CMakeLists.txt:5 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp"
  "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
