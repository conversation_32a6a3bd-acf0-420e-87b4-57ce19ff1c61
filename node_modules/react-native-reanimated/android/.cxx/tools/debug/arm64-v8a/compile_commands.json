[{"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dworklets_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreanimated_EXPORTS -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/../Common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/callinvoker -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactCommon/runtimeexecutor -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c8879d6f703d9348c0fabf96be0e0a30/transformed/hermes-android-0.79.5-debug/prefab/modules/libhermes/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.19.1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DHERMES_ENABLE_DEBUGGER=1 -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp"}]