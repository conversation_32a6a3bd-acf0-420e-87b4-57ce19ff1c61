/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=31 \
  -DANDROID_PLATFORM=android-31 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/4c146j11/obj/arm64-v8a \
  -DCMAKE_BUILD_TYPE=Debug \
  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/prefab/arm64-v8a/prefab \
  -B/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/.cxx/Debug/4c146j11/arm64-v8a \
  -GNinja \
  -DANDROID_STL=c++_shared \
  -DREACT_NATIVE_MINOR_VERSION=79 \
  -DANDROID_TOOLCHAIN=clang \
  -DREACT_NATIVE_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native \
  -DJS_RUNTIME=hermes \
  -DJS_RUNTIME_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/sdks/hermes \
  -DIS_NEW_ARCHITECTURE_ENABLED=false \
  -DIS_REANIMATED_EXAMPLE_APP=false \
  -DREANIMATED_VERSION=3.19.1 \
  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON \
  -DHERMES_ENABLE_DEBUGGER=1
