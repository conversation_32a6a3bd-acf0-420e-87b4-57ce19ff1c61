{"installationFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/intermediates/prefab_package/debug/prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.19.1", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/prefab-headers/reanimated", "moduleExportLibraries": [], "abis": []}, {"moduleName": "worklets", "moduleHeaders": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native-reanimated/android/build/prefab-headers/worklets", "moduleExportLibraries": [], "abis": []}]}}