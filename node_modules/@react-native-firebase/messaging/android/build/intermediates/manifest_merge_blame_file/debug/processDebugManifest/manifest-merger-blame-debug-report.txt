1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.invertase.firebase.messaging" >
4
5    <uses-sdk android:minSdkVersion="31" />
6
7    <uses-permission android:name="android.permission.INTERNET" />
7-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:5:3-64
7-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:5:20-62
8    <uses-permission android:name="android.permission.WAKE_LOCK" />
8-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:6:3-65
8-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:6:20-63
9    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
9-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:7:3-76
9-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:7:20-74
10
11    <application>
11-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:9:3-42:17
12        <service
12-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:10:5-11:42
13            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
13-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:10:14-73
14            android:exported="false" />
14-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:11:15-39
15        <service
15-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:12:5-17:15
16            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
16-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:12:14-65
17            android:exported="false" >
17-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:13:14-38
18            <intent-filter>
18-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:14:7-16:23
19                <action android:name="com.google.firebase.MESSAGING_EVENT" />
19-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:15:9-69
19-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:15:17-67
20            </intent-filter>
21        </service>
22
23        <receiver
23-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:18:5-25:16
24            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
24-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:19:7-59
25            android:exported="true"
25-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:20:7-30
26            android:permission="com.google.android.c2dm.permission.SEND" >
26-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:21:7-67
27            <intent-filter>
27-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:22:7-24:23
28                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
28-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:23:9-73
28-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:23:17-70
29            </intent-filter>
30        </receiver>
31
32        <meta-data
33            android:name="delivery_metrics_exported_to_big_query_enabled"
33-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:28:7-69
34            android:value="false" />
34-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:29:7-66
35        <meta-data
36            android:name="firebase_messaging_auto_init_enabled"
36-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:31:7-58
37            android:value="true" />
37-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:32:7-53
38        <meta-data
39            android:name="firebase_messaging_notification_delegation_enabled"
39-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:34:7-72
40            android:value="false" />
40-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:35:7-67
41        <meta-data
42            android:name="com.google.firebase.messaging.default_notification_channel_id"
42-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:37:7-83
43            android:value="" />
43-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:38:7-59
44        <meta-data
45            android:name="com.google.firebase.messaging.default_notification_color"
45-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:40:7-78
46            android:resource="@color/white" />
46-->/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/@react-native-firebase/messaging/android/src/main/AndroidManifest.xml:41:7-58
47    </application>
48
49</manifest>
