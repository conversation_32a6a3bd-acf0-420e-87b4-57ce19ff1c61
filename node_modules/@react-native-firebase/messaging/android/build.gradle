import io.invertase.gradle.common.PackageJson

buildscript {
  // The Android Gradle plugin is only required when opening the android folder stand-alone.
  // This avoids unnecessary downloads and potential conflicts when the library is included as a
  // module dependency in an application project.
  if (project == rootProject) {
    repositories {
      google()
      mavenCentral()
    }

    dependencies {
      classpath("com.android.tools.build:gradle:8.4.0")
    }
  }
}

plugins {
  id "io.invertase.gradle.build" version "1.5"
}

def appProject
if (findProject(':@react-native-firebase_app')) {
  appProject = project(':@react-native-firebase_app')
} else if (findProject(':react-native-firebase_app')) {
  appProject = project(':react-native-firebase_app')
} else {
  throw new GradleException('Could not find the react-native-firebase/app package, have you installed it?')
}
def packageJson = PackageJson.getForProject(project)
def appPackageJson = PackageJson.getForProject(appProject)
def firebaseBomVersion = appPackageJson['sdkVersions']['android']['firebase']
def jsonMinSdk = appPackageJson['sdkVersions']['android']['minSdk']
def jsonTargetSdk = appPackageJson['sdkVersions']['android']['targetSdk']
def jsonCompileSdk = appPackageJson['sdkVersions']['android']['compileSdk']
def coreVersionDetected = appPackageJson['version']
def coreVersionRequired = packageJson['peerDependencies'][appPackageJson['name']]
// Only log after build completed so log warning appears at the end
if (coreVersionDetected != coreVersionRequired) {
  gradle.buildFinished {
    project.logger.warn("ReactNativeFirebase WARNING: NPM package '${packageJson['name']}' depends on '${appPackageJson['name']}' v${coreVersionRequired} but found v${coreVersionDetected}, this might cause build issues or runtime crashes.")
  }
}

project.ext {
  set('react-native', [
    versions: [
      android : [
        minSdk    : jsonMinSdk,
        targetSdk : jsonTargetSdk,
        compileSdk: jsonCompileSdk,
      ],

      firebase: [
        bom: firebaseBomVersion,
      ],
    ],
  ])
}

apply from: file('./../../app/android/firebase-json.gradle')

String defaultNotificationChannelId = ''
String notificationChannelId = defaultNotificationChannelId
String defaultNotificationColor = '@color/white'
String notificationColor = defaultNotificationColor

String deliveryMetricsExportEnabled = 'false'

// If nothing is defined, data collection defaults to true
String dataCollectionEnabled = 'true'

String notificationDelegationEnabled = 'false'

if (rootProject.ext && rootProject.ext.firebaseJson) {
  /* groovylint-disable-next-line NoDef, VariableTypeRequired */
  def rnfbConfig = rootProject.ext.firebaseJson
  // If the SDK-wide data collection flag is defined and false, our default flips to false
  if (rnfbConfig.isFlagEnabled('app_data_collection_default_enabled', true) == false) {
    dataCollectionEnabled = 'false'
  }
  // If our specific collection flag is defined, it's status takes precedence
  if (rnfbConfig.isDefined('messaging_auto_init_enabled')) {
    if (rnfbConfig.isFlagEnabled('messaging_auto_init_enabled', true)) {
      dataCollectionEnabled = 'true'
    } else {
      dataCollectionEnabled = 'false'
    }
  }

  if (rnfbConfig.isFlagEnabled('messaging_android_notification_delivery_metrics_export_enabled', false)) {
    deliveryMetricsExportEnabled = 'true'
  }
  notificationChannelId = rnfbConfig.getStringValue('messaging_android_notification_channel_id', defaultNotificationChannelId)
  notificationColor = rnfbConfig.getStringValue('messaging_android_notification_color', defaultNotificationColor)

  if (rnfbConfig.isFlagEnabled('messaging_android_notification_delegation_enabled', false)) {
    notificationDelegationEnabled = 'true'
  }
}

android {
  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION.tokenize('.')[0].toInteger()
  if (agpVersion >= 7) {
    namespace = 'io.invertase.firebase.messaging'
  }

  defaultConfig {
    multiDexEnabled = true
    manifestPlaceholders = [
      firebaseJsonDeliveryMetricsExportEnabled: deliveryMetricsExportEnabled,
      firebaseJsonAutoInitEnabled: dataCollectionEnabled,
      firebaseJsonNotificationChannelId: notificationChannelId,
      firebaseJsonNotificationColor: notificationColor,
      firebaseJsonNotificationDelegationEnabled: notificationDelegationEnabled
    ]
  }

  buildFeatures {
    // AGP 8 no longer builds config by default
    buildConfig = true
  }

  lintOptions {
    disable 'GradleCompatible'
    abortOnError = false
  }

  if (agpVersion < 8) {
    compileOptions {
      sourceCompatibility = JavaVersion.VERSION_11
      targetCompatibility = JavaVersion.VERSION_11
    }
  }
}

repositories {
  google()
  mavenCentral()
}

dependencies {
  api appProject
  implementation platform("com.google.firebase:firebase-bom:${ReactNative.ext.getVersion("firebase", "bom")}")
  implementation "com.google.firebase:firebase-messaging"
}

ReactNative.shared.applyPackageVersion()
ReactNative.shared.applyDefaultExcludes()
ReactNative.module.applyAndroidVersions()
ReactNative.module.applyReactNativeDependency("api")
