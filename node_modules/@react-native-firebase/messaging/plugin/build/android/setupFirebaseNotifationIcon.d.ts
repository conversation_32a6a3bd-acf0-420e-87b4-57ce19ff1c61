import { ConfigPlugin } from '@expo/config-plugins';
import { ManifestApplication } from '@expo/config-plugins/build/android/Manifest';
import { ExpoConfig } from '@expo/config-types';
/**
 * Create `com.google.firebase.messaging.default_notification_icon` and `com.google.firebase.messaging.default_notification_color`
 */
export declare const withExpoPluginFirebaseNotification: ConfigPlugin;
export declare function setFireBaseMessagingAndroidManifest(config: ExpoConfig, application: ManifestApplication): ManifestApplication | ExpoConfig;
