"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_plugins_1 = require("@expo/config-plugins");
const android_1 = require("./android");
/**
 * A config plugin for configuring `@react-native-firebase/app`
 */
const withRnFirebaseApp = config => {
    return (0, config_plugins_1.withPlugins)(config, [
        // iOS
        // Android
        android_1.withExpoPluginFirebaseNotification,
    ]);
};
const pak = require('@react-native-firebase/messaging/package.json');
exports.default = (0, config_plugins_1.createRunOncePlugin)(withRnFirebaseApp, pak.name, pak.version);
