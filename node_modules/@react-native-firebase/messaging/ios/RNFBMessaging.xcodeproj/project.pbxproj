// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		2744B98621F45429004F8E3F /* RNFBMessagingModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 2744B98521F45429004F8E3F /* RNFBMessagingModule.m */; };
		27F24640242AA29F0098906C /* RNFBMessaging+UNUserNotificationCenter.m in Sources */ = {isa = PBXBuildFile; fileRef = 27F2463F242AA29E0098906C /* RNFBMessaging+UNUserNotificationCenter.m */; };
		27F24643242AA2EE0098906C /* RNFBMessaging+AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 27F24642242AA2EE0098906C /* RNFBMessaging+AppDelegate.m */; };
		27F24646242AA30E0098906C /* RNFBMessaging+FIRMessagingDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 27F24645242AA30E0098906C /* RNFBMessaging+FIRMessagingDelegate.m */; };
		27F2464D242AA7330098906C /* RNFBMessaging+NSNotificationCenter.m in Sources */ = {isa = PBXBuildFile; fileRef = 27F2464C242AA7330098906C /* RNFBMessaging+NSNotificationCenter.m */; };
		37D60F7424E647BF00E44930 /* RNFBMessaging+UNNotificationServiceExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 37D60F7324E647BF00E44930 /* RNFBMessaging+UNNotificationServiceExtension.m */; };
		DA446E5222CA48690066A0A3 /* RNFBMessagingSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = DA446E5122CA48690066A0A3 /* RNFBMessagingSerializer.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		2744B98021F45429004F8E3F /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		2744B98221F45429004F8E3F /* libRNFBMessaging.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNFBMessaging.a; sourceTree = BUILT_PRODUCTS_DIR; };
		2744B98421F45429004F8E3F /* RNFBMessagingModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = RNFBMessagingModule.h; path = RNFBMessaging/RNFBMessagingModule.h; sourceTree = SOURCE_ROOT; };
		2744B98521F45429004F8E3F /* RNFBMessagingModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = RNFBMessagingModule.m; path = RNFBMessaging/RNFBMessagingModule.m; sourceTree = SOURCE_ROOT; };
		27F2463E242AA29E0098906C /* RNFBMessaging+UNUserNotificationCenter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RNFBMessaging+UNUserNotificationCenter.h"; sourceTree = "<group>"; };
		27F2463F242AA29E0098906C /* RNFBMessaging+UNUserNotificationCenter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "RNFBMessaging+UNUserNotificationCenter.m"; sourceTree = "<group>"; };
		27F24641242AA2EE0098906C /* RNFBMessaging+AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RNFBMessaging+AppDelegate.h"; sourceTree = "<group>"; };
		27F24642242AA2EE0098906C /* RNFBMessaging+AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "RNFBMessaging+AppDelegate.m"; sourceTree = "<group>"; };
		27F24644242AA30E0098906C /* RNFBMessaging+FIRMessagingDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RNFBMessaging+FIRMessagingDelegate.h"; sourceTree = "<group>"; };
		27F24645242AA30E0098906C /* RNFBMessaging+FIRMessagingDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "RNFBMessaging+FIRMessagingDelegate.m"; sourceTree = "<group>"; };
		27F2464B242AA7330098906C /* RNFBMessaging+NSNotificationCenter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RNFBMessaging+NSNotificationCenter.h"; sourceTree = "<group>"; };
		27F2464C242AA7330098906C /* RNFBMessaging+NSNotificationCenter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "RNFBMessaging+NSNotificationCenter.m"; sourceTree = "<group>"; };
		37D60F7124E6465300E44930 /* RNFBMessaging+UNNotificationServiceExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RNFBMessaging+UNNotificationServiceExtension.h"; sourceTree = "<group>"; };
		37D60F7324E647BF00E44930 /* RNFBMessaging+UNNotificationServiceExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "RNFBMessaging+UNNotificationServiceExtension.m"; sourceTree = "<group>"; };
		DA446E5022CA485C0066A0A3 /* RNFBMessagingSerializer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNFBMessagingSerializer.h; sourceTree = "<group>"; };
		DA446E5122CA48690066A0A3 /* RNFBMessagingSerializer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNFBMessagingSerializer.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2744B97F21F45429004F8E3F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2744B97521F452B8004F8E3F /* Products */ = {
			isa = PBXGroup;
			children = (
				2744B98221F45429004F8E3F /* libRNFBMessaging.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2744B98321F45429004F8E3F /* RNFBMessaging */ = {
			isa = PBXGroup;
			children = (
				37D60F7324E647BF00E44930 /* RNFBMessaging+UNNotificationServiceExtension.m */,
				37D60F7124E6465300E44930 /* RNFBMessaging+UNNotificationServiceExtension.h */,
				2744B98421F45429004F8E3F /* RNFBMessagingModule.h */,
				2744B98521F45429004F8E3F /* RNFBMessagingModule.m */,
				DA446E5022CA485C0066A0A3 /* RNFBMessagingSerializer.h */,
				DA446E5122CA48690066A0A3 /* RNFBMessagingSerializer.m */,
				27F2463E242AA29E0098906C /* RNFBMessaging+UNUserNotificationCenter.h */,
				27F2463F242AA29E0098906C /* RNFBMessaging+UNUserNotificationCenter.m */,
				27F24641242AA2EE0098906C /* RNFBMessaging+AppDelegate.h */,
				27F24642242AA2EE0098906C /* RNFBMessaging+AppDelegate.m */,
				27F24644242AA30E0098906C /* RNFBMessaging+FIRMessagingDelegate.h */,
				27F24645242AA30E0098906C /* RNFBMessaging+FIRMessagingDelegate.m */,
				27F2464B242AA7330098906C /* RNFBMessaging+NSNotificationCenter.h */,
				27F2464C242AA7330098906C /* RNFBMessaging+NSNotificationCenter.m */,
			);
			path = RNFBMessaging;
			sourceTree = "<group>";
		};
		3323F52AAFE26B7384BE4DE3 = {
			isa = PBXGroup;
			children = (
				2744B98321F45429004F8E3F /* RNFBMessaging */,
				2744B97521F452B8004F8E3F /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2744B98121F45429004F8E3F /* RNFBMessaging */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2744B98821F45429004F8E3F /* Build configuration list for PBXNativeTarget "RNFBMessaging" */;
			buildPhases = (
				2744B97E21F45429004F8E3F /* Sources */,
				2744B97F21F45429004F8E3F /* Frameworks */,
				2744B98021F45429004F8E3F /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNFBMessaging;
			productName = RNFBMessaging;
			productReference = 2744B98221F45429004F8E3F /* libRNFBMessaging.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3323F95273A95DB34F55C6D7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = RNFBMessaging;
				LastUpgradeCheck = 1010;
				ORGANIZATIONNAME = Invertase;
				TargetAttributes = {
					2744B98121F45429004F8E3F = {
						CreatedOnToolsVersion = 10.1;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 3323F1C5716BA966BBBB95A4 /* Build configuration list for PBXProject "RNFBMessaging" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 3323F52AAFE26B7384BE4DE3;
			productRefGroup = 2744B97521F452B8004F8E3F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2744B98121F45429004F8E3F /* RNFBMessaging */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		2744B97E21F45429004F8E3F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2744B98621F45429004F8E3F /* RNFBMessagingModule.m in Sources */,
				27F24643242AA2EE0098906C /* RNFBMessaging+AppDelegate.m in Sources */,
				37D60F7424E647BF00E44930 /* RNFBMessaging+UNNotificationServiceExtension.m in Sources */,
				27F24640242AA29F0098906C /* RNFBMessaging+UNUserNotificationCenter.m in Sources */,
				DA446E5222CA48690066A0A3 /* RNFBMessagingSerializer.m in Sources */,
				27F2464D242AA7330098906C /* RNFBMessaging+NSNotificationCenter.m in Sources */,
				27F24646242AA30E0098906C /* RNFBMessaging+FIRMessagingDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		2744B98921F45429004F8E3F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2744B98A21F45429004F8E3F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		3323F77D701E1896E6D239CF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"${BUILT_PRODUCTS_DIR}/**",
					"${SRCROOT}/../../../ios/Firebase/**",
					"$(FIREBASE_SEARCH_PATH)/Firebase/**",
					"$(SRCROOT)/../../../ios/Pods/FirebaseMessaging/Frameworks",
					"$(SRCROOT)/../../../tests/ios/Pods/FirebaseMessaging/Frameworks",
				);
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(REACT_SEARCH_PATH)/React/**",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../react-native-firebase/ios/**",
					"$(FIREBASE_SEARCH_PATH)/Firebase/**",
					"${SRCROOT}/../../../ios/Firebase/**",
					"${SRCROOT}/../../../ios/Pods/Headers/Public/**",
					"${SRCROOT}/../../../tests/ios/Pods/Headers/Public/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native-firebase/ios/**",
					"$(SRCROOT)/../../../packages/app/ios/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		3323F7E33E1559A2B9826720 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"${BUILT_PRODUCTS_DIR}/**",
					"${SRCROOT}/../../../ios/Firebase/**",
					"$(FIREBASE_SEARCH_PATH)/Firebase/**",
					"$(SRCROOT)/../../../ios/Pods/FirebaseMessaging/Frameworks",
				);
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(REACT_SEARCH_PATH)/React/**",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../react-native-firebase/ios/**",
					"$(FIREBASE_SEARCH_PATH)/Firebase/**",
					"${SRCROOT}/../../../ios/Firebase/**",
					"${SRCROOT}/../../../ios/Pods/Headers/Public/**",
					"${SRCROOT}/../../../tests/ios/Pods/Headers/Public/**",
					"$(SRCROOT)/../../../node_modules/react-native/React/**",
					"$(SRCROOT)/../../../node_modules/react-native-firebase/ios/**",
					"$(SRCROOT)/../../../packages/app/ios/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACH_O_TYPE = staticlib;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2744B98821F45429004F8E3F /* Build configuration list for PBXNativeTarget "RNFBMessaging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2744B98921F45429004F8E3F /* Debug */,
				2744B98A21F45429004F8E3F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3323F1C5716BA966BBBB95A4 /* Build configuration list for PBXProject "RNFBMessaging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3323F7E33E1559A2B9826720 /* Debug */,
				3323F77D701E1896E6D239CF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 3323F95273A95DB34F55C6D7 /* Project object */;
}
