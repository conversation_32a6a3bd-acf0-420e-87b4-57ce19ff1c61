{"name": "@react-native-firebase/messaging", "version": "23.4.0", "author": "Invertase <<EMAIL>> (http://invertase.io)", "description": "React Native Firebase - React Native Firebase provides native integration of Firebase Cloud Messaging (FCM) for both Android & iOS. FCM is a cost free service, allowing for server-device and device-device communication. The React Native Firebase Messaging module provides a simple JavaScript API to interact with FCM.", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "genversion --semi lib/version.js", "build:clean": "rimraf android/build && rimraf ios/build", "build:plugin": "rimraf plugin/build && tsc --build plugin", "lint:plugin": "eslint plugin/src/*", "prepare": "yarn run build && yarn run build:plugin"}, "repository": {"type": "git", "url": "https://github.com/invertase/react-native-firebase/tree/main/packages/messaging"}, "license": "Apache-2.0", "keywords": ["react", "react-native", "firebase", "messaging"], "peerDependencies": {"@react-native-firebase/app": "23.4.0", "expo": ">=47.0.0"}, "devDependencies": {"expo": "^53.0.20"}, "peerDependenciesMeta": {"expo": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "gitHead": "86e646d909ac3d1d02ea51f38a486a4936660589"}