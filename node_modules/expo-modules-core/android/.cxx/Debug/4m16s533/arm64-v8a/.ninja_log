# ninja log v5
5	2443	1759982287304068220	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o	fee71619116722a1
3	2447	1759982287305798954	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	275f7fa703a3a696
11	2490	1759982287356194990	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o	735b9eab7e6a589f
7	2557	1759982287420786728	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o	f6e65bbc10459dd0
19	2649	1759982287507232337	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	89e764c33bc74933
14	2919	1759982287776508492	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	c011030f663e9daa
9	2931	1759982287794932175	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o	c0dedf2af7d5cc25
0	3582	1759982288430658002	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	9e269ac12c4fd56e
23	4349	1759982289205765478	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	b5de06c091631d48
2649	4397	1759982289261876076	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o	864526d9ab987f57
0	5159	1759982290016568523	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o	7509d7139a324429
17	5598	1759982290453350953	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	440f7dd7ccad7c85
2931	5616	1759982290476760353	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	60be201e80514573
19	6109	1759982290957623592	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	bb90aacf8427e770
2445	6836	1759982291658136894	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	6f890fc8499bc145
2557	7506	1759982292355867582	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	9281a8508613a61d
4350	9525	1759982294387898864	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	76c9aa55484e7528
2490	9632	1759982294484451723	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o	7ee22d2f7952520c
3583	9778	1759982294600950765	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	ebe3e9a801014225
2447	10472	1759982295324137621	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o	343ac670ba57e82c
5160	10603	1759982295459604001	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	b25da95b428e9cce
7507	10662	1759982295507573992	CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o	5814b76d10da9136
4397	11057	1759982295908043592	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	6459321f5f233d92
5598	11112	1759982295970265234	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	c6d77c3d6d1e09a7
6110	11127	1759982295989850286	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o	ed23c06b21f648d8
5616	11166	1759982296027315040	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	89099916eb614482
9778	11713	1759982296578356846	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	78496bc3cac767cd
6837	12607	1759982297461262960	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	28ecef77363c8a05
9525	12619	1759982297479974660	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	30191413544a87d0
9632	12627	1759982297489881184	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	46e353ed15159bd4
10603	13061	1759982297920956955	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	ce862441917511d9
11166	13479	1759982298339700441	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o	1d1825655d62b02a
10472	13761	1759982298621516017	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	ceffe59aec84a1e8
11128	13776	1759982298637439424	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o	288a0d08ff5359da
11714	13800	1759982298662876120	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o	b3a7a68f5eb0ac74
10662	13896	1759982298755857923	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	509e5fd68b714949
11057	13954	1759982298815056257	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o	2bee761cd949138c
2919	13975	1759982298822352751	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	6a123c5e661e0c03
11112	14042	1759982298903798533	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o	b2e73005486bc880
12607	14131	1759982298994427553	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o	abdb3227a88b2cd5
14131	14316	1759982299160859667	../../../../build/intermediates/cxx/Debug/4m16s533/obj/arm64-v8a/libexpo-modules-core.so	30d643a7bdb58984
