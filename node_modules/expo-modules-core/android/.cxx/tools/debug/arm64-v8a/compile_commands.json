[{"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/Exceptions.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/Exceptions.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/ExpoModulesHostObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/ExpoModulesHostObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIDeallocator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIDeallocator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIFunctionBody.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIFunctionBody.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIInjector.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIInjector.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIUtils.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JNIUtils.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JSIContext.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JSIContext.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JSReferencesCache.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JSReferencesCache.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JSharedObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JSharedObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaCallback.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaCallback.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaReferencesCache.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaReferencesCache.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptFunction.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptFunction.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptModuleObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptModuleObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptRuntime.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptRuntime.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptTypedArray.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptTypedArray.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptValue.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptValue.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptWeakObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptWeakObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/MethodMetadata.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/MethodMetadata.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/RuntimeHolder.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/RuntimeHolder.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/WeakRuntimeHolder.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/WeakRuntimeHolder.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/AnyType.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/AnyType.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/ExpectedType.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/ExpectedType.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverter.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverter.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverterProvider.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverterProvider.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/JNIToJSIConverter.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/types/JNIToJSIConverter.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSClassesDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSClassesDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSConstantsDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSConstantsDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSFunctionsDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSFunctionsDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSObjectDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSObjectDecorator.cpp"}, {"directory": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/.cxx/Debug/4m16s533/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android31 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dexpo_modules_core_EXPORTS -I/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/../common/cpp -I/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/fabric -isystem /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -std=gnu++20 -o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o -c /Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSPropertiesDecorator.cpp", "file": "/Users/<USER>/Documents/i-Jobs/dhamma-noti-android/app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSPropertiesDecorator.cpp"}]