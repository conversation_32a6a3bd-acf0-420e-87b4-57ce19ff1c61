export { default as Header } from "../AppHeader";

import { default as Label } from "../AppLabel";
import { default as Header } from "../AppHeader";
import { default as View } from "../AppView";

import { default as service } from "../../services/service";
import { default as color } from "../../AppColor";
import { default as icon } from "../icon";

import { default as DivLine } from "../DivLine";
import { default as ConnectionFailure } from "../ConnectionFailure";
import { default as RefreshControl } from "../AppRefreshControl";
// import { default as Alert } from "../Alert";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useTheme } from "@react-navigation/native";
import { useIsFocused } from "@react-navigation/native";

import AppText from "../../AppText";
import api from "../../api";
import BottomSheetModal, { BottomSheet } from "../BottomSheetModal";

import SearchBar from "../SearchBar";
import AppConfig from "../../configApp";
import YearMonthPicker from "../YearMonthPicker";
import MediaContainer from "../MediaContainer";
import { dataTranslation, t } from "../Translation";
import Moment from "../Moment";
import Image from "../Image";
import PushNotifacation from "@components/PushNotifacation";
// import "moment/locale/th";

const dataName = (props) => {
  const { item, name } = props;
  const { i18n } = useTranslation();
  try {
    return i18n.language == "th"
      ? item[`${name}_th`]
      : item[`${name}_en`] || item[`${name}_th`];
  } catch {
    return "";
  }
};

export const Service = service;
export const API = api;
const App = {
  PushNotifacation,
  dataTranslation,
  dataName,
  Dom: {
    Dispatch: useDispatch,
    Theme: useTheme,
    Translation: useTranslation,
    Focused: useIsFocused,
  },
  ...Moment,
  Label: Label,
  Header: Header,
  View: View,
  service: service,
  color: color,
  icon: icon,
  t: t,
  DivLine: DivLine,
  ConnectionFailure: ConnectionFailure,
  RefreshControl: RefreshControl,
  // Alert,
  API: api,
  BottomSheet,
  BottomSheetModal,
  text: AppText,
  SearchBar,
  AppConfig,
  YearMonthPicker,
  MediaContainer,
  Image,
};

export default App;
