{"name": "dhamma", "version": "1.0.0", "main": "index.js", "private": true, "scripts": {"start": "expo start --dev-client --reset-cache --clear", "android": "expo run:android", "ios": "expo run:ios ", "web": "expo start --web", "eject": " npx expo eject", "ad": "expo run:android ", "check": "npm outdated", "check-upd": "npx npm-check-updates", "upd": "npm update --save --force", "i": "rm -rf node_modules && rm -rf package-lock.json && npm i --force", "pod": "cd ios && rm -rf Pods && rm -rf build && pod cache clean --all && arch -x86_64 pod install --verbose --repo-update && cd ..", "build:ios": "react-native bundle --entry-file='index.js' --bundle-output='./ios/main.jsbundle'  --assets-dest --dev=false --platform='ios'", "clean:android": "cd android && ./gradlew clean && ./gradlew assembleRelease && cd ../ ", "clean:ios": "cd ios && xcodebuild clean && cd ../", "build:android": "cd android && ./gradlew assembleRelease && cd ../ ", "link:font": "npx react-native-asset", "prebuild:ios": "npx expo prebuild --platform ios --clean && npm run link:font", "prebuild:ad": "npx expo prebuild --platform android --clean && npm run link:font", "build:aab": "npx react-native build-android --mode=release"}, "dependencies": {"@expo/config-plugins": "^10.1.2", "@expo/metro-config": "~0.20.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "~4.6.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "^4.5.7", "@react-native-firebase/analytics": "^22.4.0", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/database": "^22.4.0", "@react-native-firebase/messaging": "^23.4.0", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "~6.6.1", "@react-navigation/drawer": "~6.7.2", "@react-navigation/native": "~6.1.18", "@react-navigation/stack": "~6.4.1", "@shopify/flash-list": "^1.8.3", "axios": "^1.10.0", "dayjs": "^1.11.13", "expo": "53.0.22", "expo-asset": "~11.1.5", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-doctor": "^1.13.5", "expo-file-system": "^18.1.11", "expo-font": "^13.3.1", "expo-image": "^2.2.0", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-screen-orientation": "~8.1.7", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-updates": "~0.28.13", "expo-video": "~2.2.2", "fuse.js": "^7.1.0", "i18next": "^25.5.2", "jsrsasign": "^11.1.0", "moment": "~2.30.1", "react": "~19.0.0", "react-i18next": "^15.7.3", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "^2.8.1", "react-native-keyboard-controller": "^1.17.0", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "~4.16.0", "react-native-svg": "^15.12.0", "react-native-track-player": "^4.1.1-nightly-c46c8fb107e0de61d9b33c28e7228002077b4f20", "react-native-video": "^6.15.0", "react-native-webview": "^13.15.0", "react-redux": "~9.2.0", "redux": "^5.0.1", "redux-thunk": "~3.1.0"}, "resolutions": {"glob": "~7.2.0", "@expo/prebuild-config": "~4.0.0", "expo-modules-autolinking": "~0.8.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@react-native-community/cli": "~20.0.1", "@react-native-community/cli-platform-android": "~20.0.1", "@react-native-community/cli-platform-ios": "~20.0.1", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "~5.0.1", "babel-preset-expo": "^13.2.4", "babel-preset-react-native": "~4.0.1", "loader-utils": "~3.3.1", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-track-player", "jsrsasign", "moment", "react-scripts", "expo-doctor"], "listUnknownPackages": false}}}}